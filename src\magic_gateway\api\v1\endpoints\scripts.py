import uuid
from datetime import datetime
from typing import Any, Dict, List

# Import Request for injection
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from fastapi.responses import StreamingResponse

# Import pg_parser library
from pg_parser.pg_ast_parser import PgAstParser

# Import settings for table mappings
from magic_gateway.core.config import settings

# Import the view checker script
from magic_gateway.utils.utils import get_axis_type_from_view_name
from magic_gateway.scripts.pg_to_ch_view_checker import check_pg_to_ch_view_conversion

# Import new dependencies and models
from magic_gateway.api.deps import (
    get_request_tracker_service,
    track_request_details,
    update_request_context,
)
from magic_gateway.tracking.service import RequestTrackingService

# Import shared API models (likely defined in magic_gateway/api/v1/models.py)
# Ensure these models exist and are correctly defined
from magic_gateway.api.v1.models import (
    ScriptInfo,
    ScriptRequest,
    ScriptResponse,
    User,
    PgToClickHouseConversionRequest,
    PgToClickHouseConversionResponse,
    PgToChViewCheckerRequest,
    PgToChViewCheckerResponse,
    ExportFormat,
)

from magic_gateway.auth.dependencies import get_current_active_user  # Keep auth deps
from magic_gateway.core.exceptions import (
    ScriptExecutionException,
    ForbiddenException,
    PostgresException,
    ClickHouseException,
)  # Keep exceptions
from magic_gateway.core.logging_config import log
from magic_gateway.db.postgres_handler import PostgresHandler
from magic_gateway.db.clickhouse_handler import ClickHouseHandler
from magic_gateway.utils import (
    get_formatted_column_mapping,
    generate_non_blocking_excel_response,
)
from magic_gateway.utils.parquet_processor import (
    read_parquet_file_async,
    create_parquet_file_response,
    convert_parquet_to_csv_stream,
    cleanup_parquet_file,
    group_parquet_data_by_periods,
)
from magic_gateway.utils.async_export import (
    generate_period_separated_excel_response,
    generate_intelligent_export_response,
)

# Assuming ScriptRunner exists and has been adapted or can be used as is
# Import the actual runner and its dependency getter
from magic_gateway.scripts.runner import ScriptRunner  # Assuming this exists
from magic_gateway.api.deps import get_script_runner  # Assuming this exists in deps.py

router = APIRouter()


@router.get("/", response_model=List[ScriptInfo])
async def list_scripts(
    request: Request,  # Inject Request
    request_id: uuid.UUID = Depends(
        track_request_details
    ),  # Get request_id (optional here, but consistent)
    current_user: User = Depends(get_current_active_user),
    script_runner: ScriptRunner = Depends(get_script_runner),
    _: RequestTrackingService = Depends(
        get_request_tracker_service
    ),  # Unused but required for middleware
) -> Any:
    """List all available scripts, filtered by user permissions."""
    # Update tracker with username
    await update_request_context(request, username=current_user.username)

    # Add optional task details
    task_details = {"task_type": "script_list"}
    await update_request_context(request, task_details=task_details)

    try:
        # Use await if the runner method is async
        all_scripts_metadata = (
            await script_runner.get_available_scripts()
        )  # Assuming this returns list of dicts/ScriptInfo

        # Filter based on admin status
        available_scripts = []
        for script_data in all_scripts_metadata:
            # Instantiate ScriptInfo to access metadata easily, handle potential errors
            try:
                info_model = ScriptInfo(**script_data)  # Validate against model
                requires_admin = info_model.metadata.requires_admin
                if not requires_admin or current_user.is_admin:
                    available_scripts.append(info_model)
            except Exception as pydantic_error:
                log.error(
                    f"Failed to parse script metadata for '{script_data.get('name')}': {pydantic_error}"
                )

        # Middleware handles completion logging (status 200 OK)
        return available_scripts
    except Exception as e:
        # Middleware will catch and log as 500
        log.error(
            f"ReqID: {str(request_id)[:8]} - Error listing scripts: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list scripts: {e}",
        )


@router.post("/run/{script_name}", response_model=ScriptResponse)
async def run_script(
    script_name: str,
    script_request: ScriptRequest,  # Contains parameters
    request: Request,  # Inject Request
    request_id: uuid.UUID = Depends(track_request_details),  # Get request_id
    current_user: User = Depends(get_current_active_user),
    script_runner: ScriptRunner = Depends(get_script_runner),
    _: RequestTrackingService = Depends(
        get_request_tracker_service
    ),  # Unused but required for middleware
) -> Any:
    """Run a specified script with given parameters."""
    # Update tracker with username
    await update_request_context(request, username=current_user.username)

    # Add script details to task_details for tracking/potential cancellation
    task_details = {
        "task_type": "script_run",
        "script_name": script_name,
        "parameters": script_request.parameters,
        # "script_run_id": None # Will be set if runner supports cancellation
    }
    await update_request_context(request, task_details=task_details)

    try:
        # 1. Check if script exists and user has permission
        # Assuming get_script_info returns details or raises NotFound
        script_info: ScriptInfo = await script_runner.get_script_info(
            script_name
        )  # Assumes runner has this method

        if (
            not script_info
        ):  # Should be handled by runner raising exception, but double check
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Script '{script_name}' not found.",
            )

        if script_info.metadata.requires_admin and not current_user.is_admin:
            raise ForbiddenException(  # Use specific ForbiddenException
                "Admin privileges required to run this script."
            )  # Middleware will log as 403

        # 2. Run the script
        # Pass request_id to the runner if it accepts it for context/logging
        # If runner supports cancellation, it should return an ID or update state
        script_result: ScriptResponse = await script_runner.run_script(
            script_name=script_name,
            params=script_request.parameters,
            # Pass context if runner uses it:
            # request_id=request_id,
            # username=current_user.username,
        )

        # If runner provided a cancellation ID (e.g., PID), update task details
        # runner_cancellation_id = getattr(script_result, 'cancellation_id', None) # Example
        # if runner_cancellation_id:
        #    task_details["script_run_id"] = runner_cancellation_id
        #    await update_request_context(request, task_details=task_details)

        # 3. Check script-specific errors reported in the response model
        if script_result.error:
            # Treat script-reported error as a client-side error (400)
            log.warning(
                f"ReqID: {str(request_id)[:8]} - Script '{script_name}' reported error: {script_result.error}"
            )
            # Raise HTTPException so middleware logs it correctly as completed with 400
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Script execution failed: {script_result.error}",
            )

        # If successful, middleware logs completion with 200 OK
        # Add final result preview to task details (optional)
        # final_details = task_details.copy()
        # final_details["result_preview"] = str(script_result.result)[:100]
        # await update_request_context(request, task_details=final_details)

        # Return the result from the script runner (must match ScriptResponse model)
        return script_result

    except ScriptExecutionException as e:
        # Runner raised an exception during execution (likely 500 error)
        # Middleware will log this failure
        log.error(
            f"ReqID: {str(request_id)[:8]} - Script execution runtime error: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,  # Treat runtime errors as 500
            detail=str(e),
        )
    except ForbiddenException as e:
        # Let the specific ForbiddenException propagate for correct 403 response
        raise e
    except HTTPException as e:
        # Re-raise known HTTPExceptions (like 404 Not Found from get_script_info)
        raise e
    except Exception as e:
        # Catch any other unexpected errors (likely 500)
        # Middleware will catch and log
        log.error(
            f"ReqID: {str(request_id)[:8]} - Unexpected error running script '{script_name}': {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred while running the script: {e}",
        )


@router.post("/pg-to-clickhouse", response_model=PgToClickHouseConversionResponse)
async def convert_pg_to_clickhouse(
    request: Request,
    conversion_request: PgToClickHouseConversionRequest,
    request_id: uuid.UUID = Depends(track_request_details),
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> Any:
    """Convert PostgreSQL object DDL to ClickHouse SQL.

    Supports different output formats:
    - normal (or empty): Complete SQL with both CTE and queries parts
    - cte_only: Only the CTE part of the SQL
    - queries_only: Only the queries part of the SQL
    - queries_split: Returns a JSON array/list of all queries (split by UNION ALL)
    - combined: Both CTE and queries parts combined
    - click_app: Returns a dictionary with 'cte' and 'queries' keys for proper separation

    """
    # Update tracker with username
    await update_request_context(request, username=current_user["username"])

    # Store task details
    task_details = {
        "task_type": "pg_to_clickhouse_conversion",
        "object_name": conversion_request.object_name,
        "id_panel": conversion_request.id_panel,
        "output_mode": conversion_request.output_mode,
    }
    await update_request_context(request, task_details=task_details)

    try:
        # Get PostgreSQL object DDL with custom timeout if provided
        custom_timeout = conversion_request.timeout_seconds
        if custom_timeout:
            log.info(
                f"Using custom timeout of {custom_timeout}s for pg-to-clickhouse conversion"
            )
            task_details["custom_timeout"] = custom_timeout
            await update_request_context(request, task_details=task_details)

        pg_object = await PostgresHandler.get_object_description(
            object_name=conversion_request.object_name,
            object_type=None,  # Removed object_type parameter
            request_id=request_id,  # Pass request_id for tracking
            timeout_seconds=custom_timeout,  # Pass custom timeout if provided
        )

        # Get the DDL string
        ddl = pg_object.get("ddl", "")
        if not ddl or ddl.startswith("-- DDL not available"):
            raise PostgresException(
                f"DDL not available for {conversion_request.object_name}"
            )

        # Get the output mode and axis type
        output_mode = conversion_request.output_mode.lower()
        axis_type = get_axis_type_from_view_name(conversion_request.object_name)
        log.info(f"Using output mode: {output_mode}")

        # Get column mapping from database
        raw_column_mapping = await get_formatted_column_mapping()

        # Convert the formatted mapping to a simple dict for pg_parser
        # The pg_parser library expects a flat dictionary, not a nested one
        column_mapping = {}
        if isinstance(raw_column_mapping, dict) and "global" in raw_column_mapping:
            column_mapping = raw_column_mapping["global"]

        # Get table mapping from settings, with id_panel if provided
        id_panel = conversion_request.id_panel
        try:
            if output_mode not in ["queries_only", "queries_split"]:
                table_mapping = (
                    {"art_product_groups_pet": f"pet.purchases_{id_panel or 1}"}
                    if axis_type in ("axsm", "fltm")
                    else settings.get_table_mapping(id_panel)
                )
            else:
                table_mapping = {
                    "cte_table": f"pet.purchases_{id_panel or 1}"
                    if axis_type in ("axsm", "fltm")
                    else "pet.household"
                    if axis_type == "axsh"
                    else "pet.ctlg_article_plus"
                    if axis_type == "axsa"
                    else "",
                    **settings.get_table_mapping(id_panel),
                }
        except Exception as e:
            log.error(f"Error getting table mapping: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting table mapping: {e}",
            )

        # Add the id_panel to the task details for tracking if provided
        if id_panel:
            task_details["id_panel"] = id_panel
            await update_request_context(request, task_details=task_details)
            log.info(f"Using table mapping with id_panel={id_panel}: {table_mapping}")
        else:
            log.info(f"Using default table mapping: {table_mapping}")

        # Process based on output mode
        # Initialize the parser with the PostgreSQL DDL, table mapping, and column mapping
        parser = PgAstParser(
            sql_string=ddl,
            pretty_format=False,
            dialect="clickhouse",  # Use ClickHouse dialect for output
            table_mapping=table_mapping,
            column_mapping=column_mapping,
            parenthesize_where=True,
        )

        # Process the SQL and get the result
        parser_result = parser.process()

        # Extract CTE and query parts
        cte_sql = parser_result.get("cte")
        query_parts = parser_result.get("queries", [])

        # Join query parts if there are multiple (e.g., from UNION ALL)
        queries_sql = query_parts[0] if query_parts else ""
        for i in range(1, len(query_parts)):
            queries_sql += f"\nUNION ALL\n{query_parts[i]}"

        if output_mode == "cte_only" and cte_sql:
            # Return only the CTE part
            return PgToClickHouseConversionResponse(
                request_id=request_id,
                original_ddl=ddl,
                clickhouse_sql=cte_sql,
                output_mode="cte_only",
                status="completed",
            )
        elif output_mode == "queries_only" and queries_sql:
            # Return only the queries part
            return PgToClickHouseConversionResponse(
                request_id=request_id,
                original_ddl=ddl,
                clickhouse_sql=queries_sql,
                output_mode="queries_only",
                status="completed",
            )
        elif output_mode == "queries_split" and query_parts:
            # Return list of all queries (split by UNION ALL) as JSON array/list
            return PgToClickHouseConversionResponse(
                request_id=request_id,
                original_ddl=ddl,
                clickhouse_sql=query_parts,  # Return the actual list of queries
                output_mode="queries_split",
                status="completed",
            )
        elif output_mode == "click_app":
            # Return dictionary with CTE and queries
            result_dict = {"cte": cte_sql, "queries": query_parts}
            return PgToClickHouseConversionResponse(
                request_id=request_id,
                original_ddl=ddl,
                clickhouse_sql=result_dict,
                output_mode="click_app",
                status="completed",
            )
        elif output_mode == "combined" and cte_sql and queries_sql:
            # Return both CTE and queries parts combined
            return PgToClickHouseConversionResponse(
                request_id=request_id,
                original_ddl=ddl,
                clickhouse_sql=f"WITH {cte_sql}\n\n{queries_sql}",
                output_mode="combined",
                status="completed",
            )
        else:  # Normal mode
            # For normal mode, we'll create a complete view statement with CTE and queries parts combined
            view_name = "pet." + conversion_request.object_name.split(".")[-1]

            try:
                # Generate the complete view SQL with CTE and queries parts combined
                # Combine CTE and query parts into a CREATE VIEW statement
                if cte_sql and queries_sql:
                    clickhouse_sql = (
                        f"CREATE VIEW {view_name} AS\nWITH {cte_sql}\n\n{queries_sql}"
                    )
                elif queries_sql:
                    clickhouse_sql = f"CREATE VIEW {view_name} AS\n{queries_sql}"
                else:
                    # Handle the case where conversion failed
                    raise Exception("Failed to convert PostgreSQL SQL to ClickHouse")

                # No formatting needed
            except Exception as e:
                log.error(f"Error generating ClickHouse view SQL: {e}", exc_info=True)
                # Just use the raw parts without the CREATE VIEW wrapper
                clickhouse_sql = (
                    f"{cte_sql}\n\n{queries_sql}" if cte_sql else queries_sql
                )

            # Return the result
            return PgToClickHouseConversionResponse(
                request_id=request_id,
                original_ddl=ddl,
                clickhouse_sql=clickhouse_sql,
                output_mode="normal",  # Return combined CTE and queries parts
                status="completed",
            )
    except PostgresException as e:
        # Middleware logs as 400
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        # Middleware logs as 500
        log.error(
            f"ReqID: {str(request_id)[:8]} - Error converting PostgreSQL to ClickHouse: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to convert PostgreSQL to ClickHouse: {e}",
        )


@router.post("/pg-to-ch-view-checker", response_model=PgToChViewCheckerResponse)
async def check_pg_to_ch_view(
    request: Request,
    checker_request: PgToChViewCheckerRequest,
    request_id: uuid.UUID = Depends(track_request_details),
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> Any:
    """Check conversion from PostgreSQL view to ClickHouse view."""
    # Update tracker with username
    await update_request_context(request, username=current_user["username"])

    # Store task details
    task_details = {
        "task_type": "pg_to_ch_view_checker",
        "pg_view_name": checker_request.pg_view_name,
        "id_panel": checker_request.id_panel,  # Store the id_panel if provided
        "query_ids": [],  # Will store all query IDs sent to databases
        "last_update": datetime.now().isoformat(),  # Track when we last updated
    }
    await update_request_context(request, task_details=task_details)

    # Create a function to update task details during execution
    async def update_task_details():
        # Only update if at least 1 second has passed since last update
        current_time = datetime.now()
        last_update = datetime.fromisoformat(task_details["last_update"])
        if (current_time - last_update).total_seconds() >= 1:
            task_details["last_update"] = current_time.isoformat()
            await update_request_context(request, task_details=task_details)
            log.debug(
                f"ReqID: {str(request_id)[:8]} - Updated task details with {len(task_details.get('query_ids', []))} query IDs"
            )

    try:
        # Call the view checker function
        result = await check_pg_to_ch_view_conversion(
            pg_view_name=checker_request.pg_view_name,
            id_panel=checker_request.id_panel,
            request_id=request_id,
            timeout_seconds=checker_request.timeout_seconds,
            task_details=task_details,  # Pass task_details to track query IDs
            update_task_details_callback=update_task_details,  # Pass the callback function
        )

        # Update the request context with the final task_details (including all query IDs)
        await update_request_context(request, task_details=task_details)

        # Log the tracked query IDs
        if "query_ids" in task_details and task_details["query_ids"]:
            log.info(
                f"ReqID: {str(request_id)[:8]} - Tracked query IDs: {', '.join(task_details['query_ids'])}"
            )

        # Return the result
        return PgToChViewCheckerResponse(
            request_id=request_id,
            pg_view_name=result["pg_view_name"],
            id_panel=result["id_panel"] if "id_panel" in result else 1,
            pg_row_count=result["pg_row_count"],
            ch_row_count=result["ch_row_count"],
            row_count_match=result["row_count_match"],
            status=result["status"],
            error=result["error"],
        )
    except PostgresException as e:
        # Middleware logs as 400
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        # Middleware logs as 500
        log.error(
            f"ReqID: {str(request_id)[:8]} - Error checking PostgreSQL to ClickHouse view: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check PostgreSQL to ClickHouse view: {e}",
        )


@router.get("/export/job/{job_id}", include_in_schema=True)
async def export_job_data(
    job_id: int,
    request: Request,
    format: ExportFormat = Query(ExportFormat.EXCEL, description="Export format"),
    separate_periods: bool = Query(
        False,
        description="Create separate Excel sheets for each period (Excel formats only)",
    ),
    intelligent_format: bool = Query(
        True,
        description="Enable intelligent format selection based on data size constraints",
    ),
    request_id: uuid.UUID = Depends(track_request_details),
    # Removed authentication dependency
) -> StreamingResponse:
    """
    Export data for the latest execution of a specific job from ClickHouse.

    This endpoint retrieves data from the most recent execution of the specified job_id
    (based on created_at timestamp) from the kpi_results database.

    This endpoint does not require authentication.

    Args:
        job_id: ID of the job to export data for
        format: Format to export data in (csv, excel, excel_facts, parquet)
        separate_periods: If True, create separate Excel sheets for each period (Excel formats only)
        intelligent_format: If True, enable intelligent format selection based on data size constraints

    Returns:
        StreamingResponse with the exported data
    """
    # Store task details without username (anonymous access)
    task_details = {
        "task_type": "job_data_export",
        "job_id": job_id,
        "export_format": format.value,
        "separate_periods": separate_periods,
        "intelligent_format": intelligent_format,
        "username": "anonymous",  # Mark as anonymous access
    }
    await update_request_context(request, task_details=task_details)

    try:
        # 1. Query metadata from metadata.results_metadata table to get final_result_table and analysis_name
        # Get the latest execution of this job_id based on created_at
        metadata_query = f"""
            SELECT final_result_table, analysis_name, job_info
            FROM metadata.results_metadata
            WHERE job_id = '{job_id}'
            ORDER BY created_at DESC
            LIMIT 1
        """

        # Generate a query ID for tracking
        clickhouse_query_id = f"{request_id}_metadata"
        task_details["query_ids"] = [clickhouse_query_id]
        await update_request_context(request, task_details=task_details)

        # Execute the query
        metadata_rows, _ = await ClickHouseHandler.execute_query(
            query=metadata_query,
            query_id=clickhouse_query_id,
            allow_write=False,
        )

        # Check if job exists
        if not metadata_rows:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No executions found for job ID {job_id}",
            )

        # Extract metadata
        metadata = metadata_rows[0]
        final_result_table = metadata.get("final_result_table")
        analysis_name = metadata.get("analysis_name", f"job_{job_id}")
        job_info = metadata.get("job_info", {})

        # Process job_info if it's a string
        if isinstance(job_info, str):
            try:
                import json

                parsed_job_info = json.loads(job_info)
                if isinstance(parsed_job_info, dict):
                    # Create a merged dictionary with both metadata and parsed job_info
                    job_info = {**metadata, **parsed_job_info}
                else:
                    # If not a dict, keep the original metadata and add job_info as a field
                    job_info = {**metadata, "job_info": job_info}
            except Exception as e:
                log.warning(f"Failed to parse job_info as JSON: {e}")
                # Keep the original metadata and add job_info as a field
                job_info = {**metadata, "job_info": job_info}
        else:
            # If job_info is already a dict or other type, merge with metadata
            job_info = {**metadata, **(job_info if isinstance(job_info, dict) else {})}

        # Log job_info type and content for debugging
        log.debug(f"Processed job_info type: {type(job_info)}")
        log.debug(f"Processed job_info content: {str(job_info)[:200]}...")

        # Sanitize filename and add job_id
        # Remove periods and sanitize analysis_name
        sanitized_name = "".join(
            c if c.isalnum() or c in "_- " else "_" for c in analysis_name
        )
        # Add job_id to filename
        filename = f"{sanitized_name}_job{job_id}"

        # Check if final_result_table exists
        if not final_result_table:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No result table found for the latest execution of job {job_id}",
            )

        # Record the export in the result_access table
        access_record_query = f"""
            INSERT INTO kpi_results.result_access
            (result_id, user_id, access_type, details)
            VALUES
            ('job_{job_id}', 'anonymous', 'export', 'Export format: {format.value}, Job ID: {job_id}, Request ID: {request_id}')
        """

        # Generate a query ID for tracking the access record
        access_record_query_id = f"{request_id}_access_record"
        task_details["query_ids"].append(access_record_query_id)
        await update_request_context(request, task_details=task_details)

        # Execute the command to record the access
        try:
            await ClickHouseHandler.execute_command(
                command=access_record_query,
                query_id=access_record_query_id,
            )
            log.info(f"Recorded export access for job_id {job_id}")
        except Exception as e:
            # Log but don't fail if recording access fails
            log.warning(f"Failed to record export access: {e}")

        # 2. Generate a query ID for tracking the export
        clickhouse_query_id = f"{request_id}_data"
        task_details["query_ids"].append(clickhouse_query_id)
        await update_request_context(request, task_details=task_details)

        # 3. Export data to temporary Parquet file using ClickHouse native functionality
        log.info(f"Using ClickHouse native Parquet export for job {job_id}")

        temp_parquet_path = None
        try:
            # Export the table to a temporary Parquet file
            (
                temp_parquet_path,
                row_count,
            ) = await ClickHouseHandler.export_table_to_parquet(
                table_name=final_result_table,
                query_id=clickhouse_query_id,
            )

            log.info(
                f"Exported {row_count} rows to temporary Parquet file: {temp_parquet_path}"
            )

            # 4. Generate response based on format
            # Use intelligent format selection if enabled
            if intelligent_format and format in [
                ExportFormat.EXCEL,
                ExportFormat.EXCEL_FACTS,
            ]:
                log.info(
                    f"Using intelligent format selection for job {job_id} (requested: {format.value})"
                )

                # Use intelligent export with format analysis
                horizontal_facts = format == ExportFormat.EXCEL_FACTS
                response = await generate_intelligent_export_response(
                    parquet_file_path=temp_parquet_path,
                    filename=filename,
                    requested_format=format.value,
                    job_info=job_info,
                    allow_format_change=True,  # Allow automatic format changes
                    horizontal_facts=horizontal_facts,
                    cleanup_source_file=True,  # Let intelligent export handle cleanup
                    separate_periods=separate_periods,  # Pass user preference for period separation
                )

                # Note: File cleanup is now handled by the intelligent export response
                return response
            elif format == ExportFormat.CSV:
                # Convert Parquet to CSV streaming response
                log.info(f"Converting Parquet to CSV for job {job_id}")

                async def csv_stream_generator():
                    try:
                        async for csv_chunk in convert_parquet_to_csv_stream(
                            temp_parquet_path
                        ):
                            if csv_chunk:
                                yield csv_chunk.encode("utf-8")
                    except Exception as e:
                        log.error(
                            f"Error converting Parquet to CSV: {e}", exc_info=True
                        )
                        yield b""  # Yield empty bytes on error
                    finally:
                        # Clean up the temporary Parquet file
                        cleanup_parquet_file(temp_parquet_path)

                return StreamingResponse(
                    content=csv_stream_generator(),
                    media_type="text/csv",
                    headers={
                        "Content-Disposition": f"attachment; filename={filename}.csv"
                    },
                )
            elif format == ExportFormat.PARQUET:
                # Return the native Parquet file directly
                log.info(f"Returning native Parquet file for job {job_id}")

                return create_parquet_file_response(
                    file_path=temp_parquet_path,
                    filename=filename,
                    cleanup_file=True,  # Clean up after sending
                )
            else:
                # For Excel formats, handle period separation if requested
                log.info(
                    f"Converting Parquet to Excel for job {job_id} (separate_periods={separate_periods})"
                )

                # Check if period separation is requested for Excel formats
                if separate_periods and format in [
                    ExportFormat.EXCEL,
                    ExportFormat.EXCEL_FACTS,
                ]:
                    log.info(f"Using period-separated Excel export for job {job_id}")

                    # Group data by periods
                    period_groups = await group_parquet_data_by_periods(
                        temp_parquet_path
                    )

                    # Generate Excel with separate sheets for each period
                    horizontal_facts = format == ExportFormat.EXCEL_FACTS
                    response = await generate_period_separated_excel_response(
                        period_groups=period_groups,
                        filename=filename,
                        job_info=job_info,
                        horizontal_facts=horizontal_facts,
                    )

                    # Clean up the temporary Parquet file
                    cleanup_parquet_file(temp_parquet_path)

                    return response
                else:
                    # Standard single-sheet Excel export
                    log.info(f"Using standard Excel export for job {job_id}")

                    # Create a data iterator that reads from the Parquet file
                    async def data_iterator():
                        try:
                            log.info(
                                f"Reading Parquet file for Excel conversion: {temp_parquet_path}"
                            )
                            async for chunk in read_parquet_file_async(
                                temp_parquet_path
                            ):
                                if chunk:
                                    yield chunk
                        except Exception as e:
                            log.error(
                                f"Error reading Parquet file for Excel: {e}",
                                exc_info=True,
                            )
                            yield []  # Return empty chunk on error
                        finally:
                            # Clean up the temporary Parquet file
                            cleanup_parquet_file(temp_parquet_path)

                    # Generate the Excel file using our non-blocking implementation
                    if format == ExportFormat.EXCEL_FACTS:
                        # Use the non-blocking implementation for facts format with horizontal facts enabled
                        log.info(f"Using Excel facts export for job {job_id}")
                        response = await generate_non_blocking_excel_response(
                            data_iterator=data_iterator(),
                            filename=filename,
                            job_info=job_info,
                            horizontal_facts=True,  # Enable horizontal facts format
                        )
                    else:  # Default Excel format
                        log.info(f"Using standard Excel export for job {job_id}")
                        response = await generate_non_blocking_excel_response(
                            data_iterator=data_iterator(),
                            filename=filename,
                            job_info=job_info,
                            horizontal_facts=False,  # Standard format
                        )

                    return response

        except Exception as export_error:
            # Clean up temporary Parquet file if it was created
            if temp_parquet_path:
                cleanup_parquet_file(temp_parquet_path)
            raise export_error

    except HTTPException:
        # Re-raise HTTP exceptions (cleanup already handled above if needed)
        raise
    except ClickHouseException as e:
        # Handle ClickHouse-specific errors
        log.error(
            f"ReqID: {str(request_id)[:8]} - ClickHouse error exporting job {job_id}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {e}",
        )
    except Exception as e:
        # Handle other errors
        log.error(
            f"ReqID: {str(request_id)[:8]} - Error exporting job {job_id}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export job data: {e}",
        )
